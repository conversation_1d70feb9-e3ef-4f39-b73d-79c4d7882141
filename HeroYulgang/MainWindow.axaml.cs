using Avalonia.Controls;
using HeroYulgang.Core;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang;

public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();

        // Khởi tạo World
        _ = World.Instance;

        // Log application start
        Logger.Instance.Info("Ứng dụng đã khởi động");
        Logger.Instance.Debug("Chế độ Debug đã được bật");

        // Tải cấu hình
        var config = ConfigManager.Instance;
        Logger.Instance.Info($"Đã tải cấu hình máy chủ: {config.ServerSettings.ServerName}");
        Logger.Instance.Debug($"Cổng máy chủ: {config.ServerSettings.GameServerPort}");
        Logger.Instance.Debug($"Số lượng người chơi tối đa: {config.ServerSettings.MaximumOnline}");

        // Khoi tao Database manager
        DatabaseManager.Instance.Initialize();

        // Test DBA để đảm bảo hoạt động đúng
        // _ = Task.Run(async () =>
        // {
        //     await RxjhServer.Database.DBATest.RunTestsAsync();
        //     await RxjhServer.Database.DBATest.TestConcurrentConnections();
        // });

        Logger.Instance.Info("Giao diện tab đã được khởi tạo thành công");
    }

    // Các method xử lý sự kiện đã được chuyển vào các View riêng biệt
    // MainView.axaml.cs sẽ xử lý các button server control
    // FeaturesView.axaml.cs sẽ xử lý các button tính năng
}
