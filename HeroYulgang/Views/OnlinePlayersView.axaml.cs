using System;
using System.Collections.ObjectModel;
using System.Linq;
using Avalonia.Controls;
using Avalonia.Interactivity;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Views
{
    public partial class OnlinePlayersView : UserControl
    {
        public ObservableCollection<Players> Players { get; set; }
        private Players? _selectedPlayer;

        public OnlinePlayersView()
        {
            InitializeComponent();
            Players = new ObservableCollection<Players>();
            SetupEventHandlers();
            LoadPlayers();
        }

        private void SetupEventHandlers()
        {
            SearchButton.Click += OnSearchClick;
            RefreshButton.Click += OnRefreshClick;
            PlayersDataGrid.SelectionChanged += OnPlayerSelectionChanged;

            KickPlayerButton.Click += OnKickPlayerClick;
            SendMessageButton.Click += OnSendMessageClick;
            TeleportToPlayerButton.Click += OnTeleportToPlayerClick;
            ViewPlayerDetailsButton.Click += OnViewPlayerDetailsClick;
            ExportPlayersButton.Click += OnExportPlayersClick;
            BroadcastMessageButton.Click += OnBroadcastMessageClick;
        }

        private void LoadPlayers()
        {
            try
            {
                // TODO: Load actual players from server
                // For now, add some sample data
                Players.Clear();

                foreach (var player in World.allConnectedChars.Values)
                {
                    Players.Add(player);
                }

                PlayersDataGrid.ItemsSource = Players;
                UpdatePlayerCount();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải danh sách players: {ex.Message}");
            }
        }

        private void UpdatePlayerCount()
        {
            TotalPlayersText.Text = Players.Count.ToString();
        }

        private void OnSearchClick(object? sender, RoutedEventArgs e)
        {
            var searchText = SearchTextBox.Text?.Trim().ToLower();
            if (string.IsNullOrEmpty(searchText))
            {
                PlayersDataGrid.ItemsSource = Players;
                return;
            }

            var filteredPlayers = Players.Where(p =>
                p.CharacterName.ToLower().Contains(searchText)
            ).ToList();

            PlayersDataGrid.ItemsSource = filteredPlayers;
        }

        private void OnRefreshClick(object? sender, RoutedEventArgs e)
        {
            LoadPlayers();
            Logger.Instance.Info("Đã làm mới danh sách players online");
        }

        private void OnPlayerSelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            _selectedPlayer = PlayersDataGrid.SelectedItem as Players;
            var hasSelection = _selectedPlayer != null;

            KickPlayerButton.IsEnabled = hasSelection;
            SendMessageButton.IsEnabled = hasSelection;
            TeleportToPlayerButton.IsEnabled = hasSelection;
            ViewPlayerDetailsButton.IsEnabled = hasSelection;
        }

        private void OnKickPlayerClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Đã kick player: {_selectedPlayer.CharacterName}");
                // TODO: Implement kick logic
            }
        }

        private void OnSendMessageClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Gửi tin nhắn đến player: {_selectedPlayer.CharacterName}");
                // TODO: Implement send message dialog
            }
        }

        private void OnTeleportToPlayerClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Teleport đến player: {_selectedPlayer.CharacterName}");
                // TODO: Implement teleport logic
            }
        }

        private void OnViewPlayerDetailsClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Xem chi tiết player: {_selectedPlayer.CharacterName}");
                // TODO: Implement player details dialog
            }
        }

        private void OnExportPlayersClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Xuất danh sách players");
            // TODO: Implement export functionality
        }

        private void OnBroadcastMessageClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Gửi thông báo toàn server");
            // TODO: Implement broadcast message dialog
        }
    }

}
