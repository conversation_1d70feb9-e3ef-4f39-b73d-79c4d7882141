using System;
using Avalonia.Controls;
using HeroYulgang.Core;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Views;

public partial class MainWindow : Window
{

    private readonly World _world;
    public MainWindow()
    {
        InitializeComponent();

        // Khởi tạo World
        _world = World.Instance;

        // Log application start
        Logger.Instance.Info("Ứng dụng đã khởi động");
        Logger.Instance.Debug("Chế độ Debug đã được bật");

        // Tải cấu hình
        var config = ConfigManager.Instance;
        Logger.Instance.Info($"Đã tải cấu hình máy chủ: {config.ServerSettings.ServerName}");
        Logger.Instance.Debug($"Cổng máy chủ: {config.ServerSettings.GameServerPort}");
        Logger.Instance.Debug($"Số lượng người chơi tối đa: {config.ServerSettings.MaximumOnline}");

        // K<PERSON>i tao Database manager
        DatabaseManager.Instance.Initialize();

        Logger.Instance.Info("Giao diện tab đã được khởi tạo thành công");
        StartServer();

    }

    private async void StartServer()
    {
        try
        {
            Logger.Instance.Info("Đang khởi động máy chủ...");

            bool success = await _world.StartAsync();

            if (success)
            {
                Logger.Instance.Info("Máy chủ đã khởi động thành công");
                Logger.Instance.Debug("Đang lắng nghe kết nối từ người chơi");
                Logger.Instance.Info("Máy chủ đã sẵn sàng phục vụ người chơi");
            }
            else
            {
                Logger.Instance.Error("Không thể khởi động máy chủ");
            }

        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi khởi động server: {ex.Message}");
        }   

    }
}
