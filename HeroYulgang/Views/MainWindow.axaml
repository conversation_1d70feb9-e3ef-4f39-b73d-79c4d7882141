<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="using:HeroYulgang.Views"
        mc:Ignorable="d" d:DesignWidth="1000" d:DesignHeight="600"
        x:Class="HeroYulgang.Views.MainWindow"
        x:CompileBindings="False"
        Title="YulgangHero Server Manager" MinWidth="1000" MinHeight="700" MaxWidth="1200" MaxHeight="800">

    <TabControl Margin="5">
        <TabItem Header="Server Main">
            <views:MainView/>
        </TabItem>

        <TabItem Header="Online Players" >
            <views:OnlinePlayersView/>
        </TabItem>

        <TabItem Header="Features" >
            <views:FeaturesView/>
        </TabItem>
    </TabControl>
</Window>
