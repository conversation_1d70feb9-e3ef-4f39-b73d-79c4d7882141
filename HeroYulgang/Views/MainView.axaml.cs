using System;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Interactivity;
using HeroYulgang.Services;

namespace HeroYulgang.Views
{
    public partial class MainView : UserControl
    {
        public MainView()
        {
            InitializeComponent();
            SetupEventHandlers();
        }

        private void SetupEventHandlers()
        {
            StartServerButton.Click += OnStartServerClick;
            StopServerButton.Click += OnStopServerClick;
            RestartServerButton.Click += OnRestartServerClick;
        }

        private async void OnStartServerClick(object? sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement server start logic
                Logger.Instance.Info("Đang khởi động server...");

                // Disable button during operation
                StartServerButton.IsEnabled = false;

                // Simulate server start
                await Task.Delay(1000);

                Logger.Instance.Info("Server đã được khởi động thành công!");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi động server: {ex.Message}");
            }
            finally
            {
                StartServerButton.IsEnabled = true;
            }
        }

        private async void OnStopServerClick(object? sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Instance.Info("Đang dừng server...");

                StopServerButton.IsEnabled = false;

                // TODO: Implement server stop logic
                await Task.Delay(1000);

                Logger.Instance.Info("Server đã được dừng!");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng server: {ex.Message}");
            }
            finally
            {
                StopServerButton.IsEnabled = true;
            }
        }

        private async void OnRestartServerClick(object? sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Instance.Info("Đang khởi động lại server...");

                RestartServerButton.IsEnabled = false;

                // TODO: Implement server restart logic
                await Task.Delay(2000);

                Logger.Instance.Info("Server đã được khởi động lại thành công!");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi động lại server: {ex.Message}");
            }
            finally
            {
                RestartServerButton.IsEnabled = true;
            }
        }
    }
}
